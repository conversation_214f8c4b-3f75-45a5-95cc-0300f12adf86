import React from 'react';
import { Helmet } from 'react-helmet-async';

interface SEOHeadProps {
  title?: string;
  description?: string;
  keywords?: string;
  image?: string;
  url?: string;
  type?: string;
  noIndex?: boolean;
}

export const SEOHead: React.FC<SEOHeadProps> = ({
  title = "Metamorphic Labs - Redefining Reality with AI, Quantum Systems & Intelligent Software",
  description = "Metamorphic Labs pioneers next-generation AI systems, multi-agent orchestration, generative creativity, and boundary-pushing software solutions. From deep prompt ecosystems to decentralized art galleries—we build the future.",
  keywords = "AI, Quantum Systems, Multi-Agent Orchestration, Generative Art, NFTs, Prompt Engineering, Catalyst, Metamorphic Reactor, Vault 024, Machine Learning, Intelligent Software",
  image = "https://metamorphiclabs.ai/og-image.jpg",
  url = "https://metamorphiclabs.ai",
  type = "website",
  noIndex = false,
}) => {
  const fullTitle = title.includes('Metamorphic Labs') ? title : `${title} | Metamorphic Labs`;

  return (
    <Helmet>
      <title>{fullTitle}</title>
      <meta name="description" content={description} />
      <meta name="keywords" content={keywords} />
      {noIndex && <meta name="robots" content="noindex, nofollow" />}
      
      {/* Open Graph */}
      <meta property="og:title" content={fullTitle} />
      <meta property="og:description" content={description} />
      <meta property="og:image" content={image} />
      <meta property="og:url" content={url} />
      <meta property="og:type" content={type} />
      <meta property="og:site_name" content="Metamorphic Labs" />
      
      {/* Twitter */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={fullTitle} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content={image} />
      <meta name="twitter:site" content="@metamorphiclabs" />
      
      {/* Canonical URL */}
      <link rel="canonical" href={url} />
    </Helmet>
  );
};
