import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { Navigation } from '../Navigation';
import { ThemeProvider } from '../ThemeProvider';

// Mock framer-motion
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: React.PropsWithChildren<Record<string, unknown>>) => <div {...props}>{children}</div>,
  },
  AnimatePresence: ({ children }: React.PropsWithChildren) => children,
}));

const NavigationWrapper = ({ children }: { children: React.ReactNode }) => (
  <BrowserRouter>
    <ThemeProvider defaultTheme="dark">
      {children}
    </ThemeProvider>
  </BrowserRouter>
);

describe('Navigation', () => {
  it('renders navigation links', () => {
    render(
      <NavigationWrapper>
        <Navigation />
      </NavigationWrapper>
    );
    
    expect(screen.getByText('Home')).toBeInTheDocument();
    expect(screen.getByText('About')).toBeInTheDocument();
    expect(screen.getByText('Systems')).toBeInTheDocument();
    expect(screen.getByText('Contact')).toBeInTheDocument();
  });

  it('renders Metamorphic Labs logo', () => {
    render(
      <NavigationWrapper>
        <Navigation />
      </NavigationWrapper>
    );

    expect(screen.getByText('Metamorphic Labs')).toBeInTheDocument();
    expect(screen.getByAltText('Metamorphic Labs')).toBeInTheDocument();
  });

  it('renders Get Started button', () => {
    render(
      <NavigationWrapper>
        <Navigation />
      </NavigationWrapper>
    );
    
    expect(screen.getByText('Get Started')).toBeInTheDocument();
  });

  it('toggles mobile menu when menu button is clicked', () => {
    render(
      <NavigationWrapper>
        <Navigation />
      </NavigationWrapper>
    );
    
    const menuButton = screen.getByLabelText('Toggle menu');
    fireEvent.click(menuButton);
    
    // Check if mobile menu items are visible
    const mobileMenuItems = screen.getAllByText('Home');
    expect(mobileMenuItems.length).toBeGreaterThan(1); // Desktop + mobile
  });

  it('has proper accessibility attributes', () => {
    render(
      <NavigationWrapper>
        <Navigation />
      </NavigationWrapper>
    );
    
    const menuButton = screen.getByLabelText('Toggle menu');
    expect(menuButton).toHaveAttribute('aria-label', 'Toggle menu');
  });
});
