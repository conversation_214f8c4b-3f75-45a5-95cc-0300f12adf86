import { useEffect } from 'react';
import { useStore } from '@/store/useStore';

export const useAccessibility = () => {
  const { prefersReducedMotion, setPrefersReducedMotion } = useStore();

  useEffect(() => {
    // Check for reduced motion preference
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    setPrefersReducedMotion(mediaQuery.matches);

    const handleChange = (e: MediaQueryListEvent) => {
      setPrefersReducedMotion(e.matches);
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, [setPrefersReducedMotion]);

  useEffect(() => {
    // Add reduced motion class to body if needed
    if (prefersReducedMotion) {
      document.body.classList.add('reduce-motion');
    } else {
      document.body.classList.remove('reduce-motion');
    }
  }, [prefersReducedMotion]);

  return {
    prefersReducedMotion,
  };
};
