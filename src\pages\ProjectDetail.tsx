import { <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowLeft, Brain, Server, Code, Target, Lightbulb, BarChart, Zap, Users, TrendingUp } from "lucide-react";

const ProjectDetail = () => {
  const { slug } = useParams();

  const projectData = {
    "ai-platform": {
      title: "Metamorphic Labs – AI Platform",
      subtitle: "Next-Generation AI Research and Development Framework",
      image: "https://images.unsplash.com/photo-1677442136019-21780ecad995?w=1200&h=600&fit=crop",
      category: "AI Platforms",
      status: "Conceptual/Design Phase",
      duration: "Ongoing",
      team: "Internal R&D",
      problem: "Current AI tools lack comprehensive integration, personalized workflows, and advanced research capabilities. Users struggle with fragmented AI experiences and limited customization options for specialized use cases.",
      solution: "A comprehensive AI platform featuring adaptive workflows, smart contextual assistants, multi-model AI chaining, and automated prompt optimization. Includes subscription-tier innovations, custom model training tools, and advanced research systems with federated privacy-focused networks.",
      impact: "Positioned to become a flagship platform for AI research and development, offering unprecedented integration of AI tools with personalized experiences, quantum-AI readiness, and comprehensive model training capabilities.",
      techStack: ["Python", "TensorFlow", "Multi-Model AI", "Custom Training", "Browser-based UI", "Mobile/Desktop Apps", "API Integration", "Quantum-AI Ready"],
      keyFeatures: [
        "Adaptive AI workflows and smart contextual assistants",
        "Multi-model AI chaining with automated prompt optimization", 
        "Custom model training and fine-tuning capabilities",
        "AI-enhanced research with deep analytics",
        "Personalized AI personas that learn user preferences",
        "Federated privacy-focused AI networks",
        "Future quantum-AI integration readiness"
      ],
      metrics: [
        { label: "AI Models", value: "Multi-Model", icon: <Brain className="h-5 w-5" /> },
        { label: "Features", value: "50+", icon: <Zap className="h-5 w-5" /> },
        { label: "Integration", value: "IDE+", icon: <Code className="h-5 w-5" /> },
        { label: "Privacy", value: "Federated", icon: <Target className="h-5 w-5" /> }
      ]
    },
    "ai-integration-broker": {
      title: "AI Integration & Broker System",
      subtitle: "Seamless Multi-AI Integration Platform",
      image: "https://images.unsplash.com/photo-1558494949-ef010cbdcc31?w=1200&h=600&fit=crop",
      category: "AI Infrastructure",
      status: "In Progress",
      duration: "6 months",
      team: "Integration Team",
      problem: "Users need to manually switch between different AI services (ChatGPT, Claude, Perplexity, Grok) for different tasks, leading to inefficient workflows and context loss between platforms.",
      solution: "An intelligent broker system that routes queries to the optimal AI model based on task type, enables multi-AI collaboration workflows, and provides secure OAuth/API key management with AES-256 encryption.",
      impact: "Creates a unified AI experience where users can leverage the strengths of multiple AI models seamlessly, with intelligent routing ensuring optimal performance for each specific task type.",
      techStack: ["OAuth", "API Integration", "AES-256 Encryption", "Multi-AI Routing", "React", "Node.js", "Secure Token Storage"],
      keyFeatures: [
        "Intelligent query routing to optimal AI models",
        "Multi-AI collaboration workflows",
        "Secure OAuth and API key management",
        "AES-256 encryption for token storage",
        "Integration with PromptBox and SaaS Suite",
        "Compliance with AI provider policies"
      ],
      metrics: [
        { label: "AI Services", value: "10+", icon: <Brain className="h-5 w-5" /> },
        { label: "Security", value: "AES-256", icon: <Zap className="h-5 w-5" /> },
        { label: "Auth", value: "OAuth", icon: <Users className="h-5 w-5" /> },
        { label: "Routing", value: "Intelligent", icon: <Target className="h-5 w-5" /> }
      ]
    },
    "saas-suite": {
      title: "Metamorphic SaaS Suite",
      subtitle: "On-Demand SaaS Generation Platform",
      image: "https://images.unsplash.com/photo-*************-afdab827c52f?w=1200&h=600&fit=crop",
      category: "SaaS Generation",
      status: "Conceptual Planning",
      duration: "12+ months",
      team: "Platform Team",
      problem: "Traditional SaaS development is time-consuming, expensive, and requires extensive technical expertise. Small businesses and entrepreneurs struggle to create custom software solutions quickly and affordably.",
      solution: "An AI/ML-driven platform that generates and deploys custom SaaS applications on demand using Metamorphic-controlled creation, automated assembly from templates, and integrated blockchain revenue sharing with EFX tokens.",
      impact: "Revolutionizes SaaS creation by enabling rapid deployment of custom applications with built-in compliance, security, and monetization features, creating new revenue streams through blockchain-based profit sharing.",
      techStack: ["AI/ML Automation", "Multi-tenant Architecture", "AWS/Azure/GCP", "Blockchain", "EFX Tokens", "Smart Contracts", "GDPR/HIPAA Compliance"],
      keyFeatures: [
        "AI-driven SaaS application generation",
        "Multi-tenant and single-tenant deployment options",
        "Blockchain revenue sharing with EFX tokens",
        "Automated compliance and security audits",
        "Marketplace for SaaS tools and revenue contracts",
        "Private internal SaaS builder for rapid deployment"
      ],
      metrics: [
        { label: "Architecture", value: "Multi-Tenant", icon: <Server className="h-5 w-5" /> },
        { label: "Revenue", value: "EFX Tokens", icon: <TrendingUp className="h-5 w-5" /> },
        { label: "Compliance", value: "Automated", icon: <Zap className="h-5 w-5" /> },
        { label: "Deployment", value: "On-Demand", icon: <Target className="h-5 w-5" /> }
      ]
    },
    "living-pipeline": {
      title: "Metamorphic Living Pipeline",
      subtitle: "AI-Optimized CI/CD Infrastructure",
      image: "https://images.unsplash.com/photo-1518432031352-d6fc5c10da5a?w=1200&h=600&fit=crop",
      category: "DevOps Infrastructure",
      status: "Conceptual Design",
      duration: "18 months",
      team: "DevOps Team",
      problem: "Traditional CI/CD pipelines are static, require manual optimization, and lack intelligent adaptation to changing requirements. Teams spend significant time on pipeline maintenance instead of development.",
      solution: "A 'living' CI/CD system that self-optimizes using AI, featuring event-driven architecture with Kafka, Go-based services, AI-enhanced build/test/deploy stages, and ML-driven performance optimization with A/B testing.",
      impact: "Transforms DevOps by creating self-improving pipelines that adapt continuously, reduce manual intervention, and optimize performance automatically through machine learning insights.",
      techStack: ["Go", "Kafka", "Kubernetes", "MongoDB", "React", "WebSockets", "Prometheus", "OAuth2/RBAC", "Machine Learning"],
      keyFeatures: [
        "Event-driven architecture with Kafka clusters",
        "AI-enhanced build, test, and deployment stages",
        "ML-driven anomaly detection and optimization",
        "Kubernetes deployment with blue-green strategies",
        "Real-time governance dashboard with approval workflows",
        "Multi-cloud and hybrid support with disaster recovery"
      ],
      metrics: [
        { label: "Architecture", value: "Event-Driven", icon: <Server className="h-5 w-5" /> },
        { label: "Optimization", value: "AI-Enhanced", icon: <Brain className="h-5 w-5" /> },
        { label: "Security", value: "OAuth2/RBAC", icon: <Zap className="h-5 w-5" /> },
        { label: "Deployment", value: "Multi-Cloud", icon: <Target className="h-5 w-5" /> }
      ]
    },
    "reactor-extension": {
      title: "Metamorphic Reactor",
      subtitle: "AI-Powered VS Code Extension",
      image: "https://images.unsplash.com/photo-1461749280684-dccba630e2f6?w=1200&h=600&fit=crop",
      category: "Developer Tools",
      status: "Beta (v0.9.0)",
      duration: "8 months",
      team: "Extension Team",
      problem: "Developers need more intelligent coding assistance that goes beyond simple autocompletion. Current tools lack multi-agent coordination and sophisticated AI-powered workflow automation for complex development tasks.",
      solution: "A VS Code extension employing multi-agent coordination and multi-LLM logic to automate coding tasks, featuring agent coordinators, loop managers, key-vault integration, and Meta-Block consensus mechanisms.",
      impact: "Nearing public release on VS Code Marketplace, poised to revolutionize developer workflows with sophisticated AI assistance and multi-agent automation capabilities.",
      techStack: ["VS Code API", "Multi-Agent Systems", "AutoGen", "Python", "Key-Vault Integration", "Multi-LLM", "Consensus Mechanisms"],
      keyFeatures: [
        "Multi-agent coordination for complex tasks",
        "Multi-LLM logic and intelligent routing",
        "Agent coordinator and loop manager systems",
        "Key-vault integration for secure operations",
        "Meta-Block consensus mechanism",
        "Comprehensive testing and QA framework"
      ],
      metrics: [
        { label: "Version", value: "v0.9.0", icon: <Code className="h-5 w-5" /> },
        { label: "Agents", value: "Multi", icon: <Brain className="h-5 w-5" /> },
        { label: "Consensus", value: "100%", icon: <Zap className="h-5 w-5" /> },
        { label: "Release", value: "Marketplace", icon: <Target className="h-5 w-5" /> }
      ]
    },
    "deepseek-integration": {
      title: "DeepSeek AI Integration",
      subtitle: "Secure AI Integration with Compliance Framework",
      image: "https://images.unsplash.com/photo-*************-bcc4688e7485?w=1200&h=600&fit=crop",
      category: "AI Risk Management",
      status: "Strategic Planning",
      duration: "6 months",
      team: "Compliance Team",
      problem: "Integrating foreign AI models like DeepSeek presents geopolitical and compliance risks while potentially offering significant technical advantages. Organizations need secure strategies to leverage these technologies.",
      solution: "A comprehensive risk management strategy featuring secure self-hosting, model forking for independence, compliance monitoring, and technical safeguards including sandboxing and audit logging with optional blockchain records.",
      impact: "Enables secure utilization of advanced AI technologies while maintaining compliance and independence, positioning Metamorphic as AI-agnostic with flexible model deployment options.",
      techStack: ["DeepSeek Model", "Docker/Kubernetes", "Self-Hosting", "Compliance Monitoring", "Blockchain Logging", "Security Auditing"],
      keyFeatures: [
        "Secure self-hosting on private infrastructure",
        "Model forking for AI independence",
        "Comprehensive compliance monitoring",
        "Sandboxed AI process isolation",
        "Blockchain-based audit logging",
        "Geopolitical risk assessment and mitigation"
      ],
      metrics: [
        { label: "Compliance", value: "GDPR/CCPA", icon: <Zap className="h-5 w-5" /> },
        { label: "Hosting", value: "Self-Hosted", icon: <Server className="h-5 w-5" /> },
        { label: "Independence", value: "Forked", icon: <Brain className="h-5 w-5" /> },
        { label: "Security", value: "Sandboxed", icon: <Target className="h-5 w-5" /> }
      ]
    },
    "mcp-integration": {
      title: "Model Context Protocol Integration",
      subtitle: "Persistent AI Context Management System",
      image: "https://images.unsplash.com/photo-1639322537228-f710d846310a?w=1200&h=600&fit=crop",
      category: "AI Infrastructure",
      status: "Conceptual Design",
      duration: "12 months",
      team: "AI Infrastructure Team",
      problem: "AI interactions lack persistent context across sessions, limiting the development of sophisticated multi-session learning and collaboration between different AI models in complex workflows.",
      solution: "MCP servers that maintain AI context across interactions, enable dynamic multi-model orchestration, support blockchain integration for secure logging, and create a decentralized AI hosting network with EFX token monetization.",
      impact: "Would create a dynamic, context-rich AI cloud enabling continuous AI agent collaboration, positioning Metamorphic as a leading decentralized AI network with persistent memory capabilities.",
      techStack: ["Model Context Protocol", "Multi-AI Orchestration", "Blockchain Integration", "EFX Tokens", "Federated Learning", "Decentralized Networks"],
      keyFeatures: [
        "Persistent AI context across interactions",
        "Dynamic multi-model AI orchestration",
        "Blockchain-secured AI action records",
        "DeepMCP decentralized AI hosting network",
        "AI monetization through EFX tokens",
        "Context-aware development and testing tools"
      ],
      metrics: [
        { label: "Context", value: "Persistent", icon: <Brain className="h-5 w-5" /> },
        { label: "Models", value: "Multi-AI", icon: <Zap className="h-5 w-5" /> },
        { label: "Network", value: "DeepMCP", icon: <Server className="h-5 w-5" /> },
        { label: "Economy", value: "EFX Tokens", icon: <TrendingUp className="h-5 w-5" /> }
      ]
    },
    "minecraft-deepmcp": {
      title: "Minecraft Control Panel (DeepMCP)",
      subtitle: "AI-Enhanced Gaming Infrastructure",
      image: "https://images.unsplash.com/photo-1542751371-adc38448a05e?w=1200&h=600&fit=crop",
      category: "Gaming Infrastructure",
      status: "Concept Stage",
      duration: "15 months",
      team: "Gaming Team",
      problem: "Minecraft server management is complex and resource-intensive, lacking intelligent automation, cross-server economies, and AI-enhanced gameplay features that could revolutionize the gaming experience.",
      solution: "AI-driven server management with auto-scaling, self-healing, and AI-generated NPCs, integrated with blockchain economy using AIQ-G tokens, and a decentralized hosting platform on MetamorphicNet with EFX token staking.",
      impact: "Positioned to revolutionize Minecraft hosting with a fully decentralized, AI-enhanced ecosystem featuring dynamic world generation, cross-server economies, and intelligent gameplay automation.",
      techStack: ["Minecraft Server API", "AI NPCs", "Blockchain", "AIQ-G Tokens", "EFX Tokens", "MetamorphicNet", "LiveScript"],
      keyFeatures: [
        "AI-driven automatic resource scaling and optimization",
        "Self-healing servers with crash detection and recovery",
        "AI-generated NPCs and dynamic world content",
        "Cross-server blockchain economy with AIQ-G tokens",
        "Decentralized hosting on MetamorphicNet",
        "Marketplace for AI-generated content and server resources"
      ],
      metrics: [
        { label: "Economy", value: "AIQ-G Token", icon: <TrendingUp className="h-5 w-5" /> },
        { label: "Hosting", value: "Decentralized", icon: <Server className="h-5 w-5" /> },
        { label: "AI Features", value: "Auto-Scaling", icon: <Brain className="h-5 w-5" /> },
        { label: "Network", value: "MetamorphicNet", icon: <Target className="h-5 w-5" /> }
      ]
    }
  };

  const project = projectData[slug as keyof typeof projectData];

  if (!project) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-4xl font-bold mb-4">Project Not Found</h1>
          <p className="text-muted-foreground mb-6">The project you're looking for doesn't exist.</p>
          <Button asChild>
            <Link to="/projects">Back to Projects</Link>
          </Button>
        </div>
      </div>
    );
  }

  const categoryColors = {
    "AI Platforms": "bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-200",
    "AI Infrastructure": "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",
    "SaaS Generation": "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
    "DevOps Infrastructure": "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200",
    "Developer Tools": "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200",
    "AI Risk Management": "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
    "Gaming Infrastructure": "bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200",
    "AI Model Suite": "bg-accent-100 text-accent-800 dark:bg-accent-900 dark:text-accent-200"
  };

  return (
    <div className="min-h-screen py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Back Button */}
        <Button variant="ghost" asChild className="mb-8">
          <Link to="/projects">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Projects
          </Link>
        </Button>

        {/* Hero Section */}
        <div className="relative mb-12 rounded-lg overflow-hidden">
          <img
            src={project.image}
            alt={project.title}
            className="w-full h-64 md:h-96 object-cover"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
          <div className="absolute bottom-0 left-0 right-0 p-8 text-white">
            <Badge className={`${categoryColors[project.category as keyof typeof categoryColors]} mb-4`}>
              {project.category}
            </Badge>
            <h1 className="text-3xl md:text-5xl font-bold mb-2">{project.title}</h1>
            <p className="text-xl text-white/90">{project.subtitle}</p>
          </div>
        </div>

        {/* Project Overview */}
        <div className="grid lg:grid-cols-3 gap-8 mb-12">
          <div className="lg:col-span-2">
            <Card>
              <CardContent className="p-8">
                <h2 className="text-2xl font-bold mb-6 flex items-center">
                  <Lightbulb className="h-6 w-6 mr-2 text-primary-600" />
                  Project Overview
                </h2>
                <div className="grid md:grid-cols-3 gap-6 mb-8">
                  <div>
                    <h3 className="font-semibold text-muted-foreground mb-1">Status</h3>
                    <p className="font-medium">{project.status}</p>
                  </div>
                  <div>
                    <h3 className="font-semibold text-muted-foreground mb-1">Duration</h3>
                    <p className="font-medium">{project.duration}</p>
                  </div>
                  <div>
                    <h3 className="font-semibold text-muted-foreground mb-1">Team</h3>
                    <p className="font-medium">{project.team}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <div>
            <Card>
              <CardContent className="p-6">
                <h3 className="font-semibold mb-4">Key Metrics</h3>
                <div className="space-y-4">
                  {project.metrics.map((metric, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className="text-primary-600 mr-2">
                          {metric.icon}
                        </div>
                        <span className="text-sm text-muted-foreground">{metric.label}</span>
                      </div>
                      <span className="font-semibold">{metric.value}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Problem, Solution, Impact */}
        <div className="space-y-8 mb-12">
          <Card>
            <CardContent className="p-8">
              <h2 className="text-2xl font-bold mb-4 flex items-center text-red-600">
                <Target className="h-6 w-6 mr-2" />
                The Challenge
              </h2>
              <p className="text-lg text-muted-foreground leading-relaxed">
                {project.problem}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-8">
              <h2 className="text-2xl font-bold mb-4 flex items-center text-blue-600">
                <Lightbulb className="h-6 w-6 mr-2" />
                Our Solution
              </h2>
              <p className="text-lg text-muted-foreground leading-relaxed mb-6">
                {project.solution}
              </p>
              {project.keyFeatures && (
                <div>
                  <h3 className="font-semibold mb-3">Key Features:</h3>
                  <ul className="list-disc list-inside space-y-2 text-muted-foreground">
                    {project.keyFeatures.map((feature, index) => (
                      <li key={index}>{feature}</li>
                    ))}
                  </ul>
                </div>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-8">
              <h2 className="text-2xl font-bold mb-4 flex items-center text-green-600">
                <TrendingUp className="h-6 w-6 mr-2" />
                Expected Impact
              </h2>
              <p className="text-lg text-muted-foreground leading-relaxed">
                {project.impact}
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Tech Stack */}
        <Card className="mb-12">
          <CardContent className="p-8">
            <h2 className="text-2xl font-bold mb-6 flex items-center">
              <Code className="h-6 w-6 mr-2 text-primary-600" />
              Technology Stack
            </h2>
            <div className="flex flex-wrap gap-2">
              {project.techStack.map((tech, index) => (
                <Badge key={index} variant="outline" className="text-sm py-1 px-3">
                  {tech}
                </Badge>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* CTA Section */}
        <Card className="bg-gradient-to-r from-primary-50 to-accent-50 dark:from-primary-950 dark:to-accent-950 border-0">
          <CardContent className="p-12 text-center">
            <h2 className="text-3xl font-bold mb-6">Interested in Contributing?</h2>
            <p className="text-lg text-muted-foreground mb-8 max-w-2xl mx-auto">
              Join the Metamorphic Labs ecosystem and help shape the future of AI-powered development tools
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild size="lg">
                <Link to="/contact">Get Involved</Link>
              </Button>
              <Button asChild variant="outline" size="lg">
                <Link to="/projects">Explore More Projects</Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default ProjectDetail;
