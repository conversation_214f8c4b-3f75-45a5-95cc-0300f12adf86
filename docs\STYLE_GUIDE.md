# Metamorphic Labs Style Guide

## Brand Identity

Metamorphic Labs represents the cutting edge of AI, quantum systems, and intelligent software. Our visual identity reflects innovation, sophistication, and the transformative power of technology.

## Color System

### Primary Palette

#### Metamorphic Gradient
- **Primary**: `#3B82F6` (sky-500)
- **Secondary**: `#9333EA` (violet-500) 
- **Accent**: `#D946EF` (fuchsia-500)
- **Gradient**: `linear-gradient(135deg, #3B82F6 0%, #9333EA 50%, #D946EF 100%)`

#### Vault 024 Special Palette
- **Gold**: `#FFD700` (24k gold)
- **Background**: `#000000` (pure black)
- **Usage**: Exclusive for Vault 024 branding and NFT-related content

#### Neutral Palette
- **Black**: `#000000` (primary background)
- **Gray 900**: `#111827` (secondary background)
- **Gray 800**: `#1F2937` (card backgrounds)
- **Gray 300**: `#D1D5DB` (primary text)
- **Gray 400**: `#9CA3AF` (secondary text)
- **White**: `#FFFFFF` (accent text)

### Usage Guidelines

1. **Primary gradient** for main CTAs, hero elements, and brand highlights
2. **Vault 024 gold** exclusively for Vault 024 content and NFT features
3. **Black background** throughout the entire site for consistency
4. **Gray variations** for cards, borders, and secondary elements

## Typography

### Font Families

#### Inter (Primary)
- **Usage**: Body text, headings, UI elements
- **Weights**: 300 (Light), 400 (Regular), 500 (Medium), 600 (SemiBold), 700 (Bold)
- **Characteristics**: Modern, clean, highly legible

#### JetBrains Mono (Code)
- **Usage**: Code snippets, technical content, monospace needs
- **Weights**: 400 (Regular), 500 (Medium), 600 (SemiBold)
- **Characteristics**: Developer-focused, excellent readability

### Type Scale

```css
/* Headings */
.text-7xl { font-size: 4.5rem; line-height: 1; }      /* Hero titles */
.text-6xl { font-size: 3.75rem; line-height: 1; }     /* Page titles */
.text-5xl { font-size: 3rem; line-height: 1; }        /* Section titles */
.text-4xl { font-size: 2.25rem; line-height: 2.5rem; } /* Subsection titles */
.text-3xl { font-size: 1.875rem; line-height: 2.25rem; } /* Card titles */
.text-2xl { font-size: 1.5rem; line-height: 2rem; }   /* Component titles */
.text-xl { font-size: 1.25rem; line-height: 1.75rem; } /* Large text */

/* Body */
.text-lg { font-size: 1.125rem; line-height: 1.75rem; } /* Large body */
.text-base { font-size: 1rem; line-height: 1.5rem; }   /* Default body */
.text-sm { font-size: 0.875rem; line-height: 1.25rem; } /* Small text */
.text-xs { font-size: 0.75rem; line-height: 1rem; }    /* Captions */
```

## Component Guidelines

### Buttons

#### Gradient Button (Primary)
```css
.btn-gradient {
  background: linear-gradient(135deg, #3B82F6 0%, #9333EA 50%, #D946EF 100%);
  color: white;
  transition: all 0.3s ease;
}
.btn-gradient:hover {
  opacity: 0.9;
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(59, 130, 246, 0.3);
}
```

#### Vault 024 Button
```css
.btn-vault {
  background: #000000;
  border: 2px solid #FFD700;
  color: #FFD700;
  transition: all 0.3s ease;
}
.btn-vault:hover {
  background: #FFD700;
  color: #000000;
  box-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
}
```

### Cards

#### Standard Card
- Background: `bg-gradient-to-br from-slate-900 to-slate-800`
- Border: `border-slate-700`
- Hover: `hover:border-primary/50 hover:glow-primary`

#### Vault 024 Card
- Background: `bg-black`
- Border: `border-gold`
- Text: `text-gold`
- Hover: `hover:glow-gold`

### Animations

#### Hover Effects
- **Translate**: `hover:translate-y-[-8px]`
- **Scale**: `hover:scale-105`
- **Glow**: Custom glow effects for different variants

#### Page Transitions
- **Fade In**: 0.6s ease-out
- **Slide In**: 0.6s ease-out with stagger
- **Reduced Motion**: Respects user preferences

## Layout Principles

### Grid System
- **Container**: `max-w-7xl mx-auto px-4 sm:px-6 lg:px-8`
- **Responsive**: Mobile-first approach
- **Spacing**: Consistent 8px grid system

### Breakpoints
- **sm**: 640px
- **md**: 768px  
- **lg**: 1024px
- **xl**: 1280px
- **2xl**: 1536px

## Accessibility Standards

### WCAG 2.2 AA Compliance
- **Color Contrast**: Minimum 4.5:1 ratio
- **Focus Indicators**: Visible focus states
- **Keyboard Navigation**: Full keyboard accessibility
- **Screen Readers**: Semantic HTML and ARIA labels

### Implementation
- Skip links for main content
- Proper heading hierarchy
- Alt text for images
- Form labels and validation
- Reduced motion support

## Brand Voice & Messaging

### Tone
- **Innovative**: Cutting-edge, forward-thinking
- **Professional**: Authoritative, trustworthy
- **Accessible**: Clear, understandable
- **Inspiring**: Visionary, transformative

### Key Messages
- "Redefining Reality with AI, Quantum Systems & Intelligent Software"
- "Building the Future of Human-AI Collaboration"
- "From Deep Prompt Ecosystems to Decentralized Art Galleries"

## Usage Examples

### Hero Section
```jsx
<h1 className="text-4xl md:text-6xl lg:text-7xl font-bold mb-6 text-white">
  Redefining Reality with{" "}
  <span className="text-gradient">AI, Quantum Systems</span>
  <br />
  & Intelligent Software
</h1>
```

### Project Card
```jsx
<Card className="bg-gradient-primary border-primary/20 hover:border-primary/40 hover:glow-primary">
  <CardContent className="p-6">
    <h3 className="text-xl font-bold text-white mb-3">Catalyst</h3>
    <p className="text-white/80">Advanced prompt engineering platform...</p>
  </CardContent>
</Card>
```

### Vault 024 Styling
```jsx
<Card className="vault-024-card hover:glow-gold">
  <CardContent className="p-6">
    <h3 className="vault-024-text text-xl font-bold">Vault 024</h3>
    <p className="text-gold/80">Exclusive AI-generated art...</p>
  </CardContent>
</Card>
```

## File Organization

```
src/
├── components/
│   ├── ui/           # shadcn/ui components
│   ├── ProjectCard.tsx
│   ├── Navigation.tsx
│   └── Footer.tsx
├── pages/            # Route components
├── store/            # Zustand state management
├── hooks/            # Custom React hooks
└── styles/
    └── index.css     # Global styles and utilities
```

This style guide ensures consistency across the Metamorphic Labs brand and provides clear guidelines for future development and content creation.
