
import { useState } from "react";
import { <PERSON>, CardContent } from "@/components/ui/card";
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Zap, Target, Cog, Brain, Workflow, Code } from "lucide-react";

const Expertise = () => {
  const [activeTab, setActiveTab] = useState("ai-platforms");

  const expertiseAreas = {
    "ai-platforms": {
      title: "AI Platforms",
      icon: <Zap className="h-8 w-8" />,
      description: "Scalable AI infrastructure and machine learning platforms",
      services: [
        {
          title: "Machine Learning Infrastructure",
          description: "Robust ML pipelines with automated training, validation, and deployment",
          technologies: ["TensorFlow", "PyTorch", "Kubernetes", "Docker", "MLflow"]
        },
        {
          title: "AI Model Development",
          description: "Custom neural networks and deep learning models for specific use cases",
          technologies: ["Neural Networks", "Computer Vision", "NLP", "Reinforcement Learning"]
        },
        {
          title: "Data Engineering",
          description: "Scalable data processing and feature engineering systems",
          technologies: ["Apache Spark", "Kafka", "Airflow", "Elasticsearch", "Redis"]
        },
        {
          title: "Model Deployment & Monitoring",
          description: "Production-ready AI systems with monitoring and continuous improvement",
          technologies: ["AWS SageMaker", "Google Cloud AI", "Prometheus", "Grafana"]
        }
      ]
    },
    "intelligent-automation": {
      title: "Intelligent Automation",
      icon: <Target className="h-8 w-8" />,
      description: "Process automation and workflow optimization using AI",
      services: [
        {
          title: "Robotic Process Automation",
          description: "Automated workflows that handle repetitive tasks with AI decision-making",
          technologies: ["UiPath", "Automation Anywhere", "Blue Prism", "Python", "Selenium"]
        },
        {
          title: "Document Processing",
          description: "Intelligent document analysis, extraction, and classification",
          technologies: ["OCR", "NLP", "Computer Vision", "Azure Form Recognizer", "Tesseract"]
        },
        {
          title: "Business Intelligence",
          description: "AI-powered analytics and predictive insights for strategic decisions",
          technologies: ["Power BI", "Tableau", "Apache Superset", "TensorFlow", "scikit-learn"]
        },
        {
          title: "Workflow Orchestration",
          description: "Smart workflow management with adaptive routing and optimization",
          technologies: ["Apache Airflow", "Prefect", "Kubernetes", "Celery", "RabbitMQ"]
        }
      ]
    },
    "custom-software": {
      title: "Custom Software",
      icon: <Cog className="h-8 w-8" />,
      description: "Bespoke software solutions tailored to your needs",
      services: [
        {
          title: "Web Applications",
          description: "Modern, responsive web applications with AI-powered features",
          technologies: ["React", "Next.js", "Node.js", "TypeScript", "Tailwind CSS"]
        },
        {
          title: "API Development",
          description: "RESTful and GraphQL APIs with intelligent data processing",
          technologies: ["FastAPI", "Express.js", "GraphQL", "PostgreSQL", "MongoDB"]
        },
        {
          title: "Cloud Architecture",
          description: "Scalable cloud infrastructure with microservices architecture",
          technologies: ["AWS", "Google Cloud", "Azure", "Terraform", "Docker"]
        },
        {
          title: "Mobile Applications",
          description: "Cross-platform mobile apps with AI capabilities",
          technologies: ["React Native", "Flutter", "Swift", "Kotlin", "Firebase"]
        }
      ]
    }
  };

  return (
    <div className="min-h-screen py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-5xl font-bold mb-6">
            Our <span className="text-gradient">Expertise</span>
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            We excel in three core areas that form the foundation of modern intelligent systems
          </p>
        </div>

        {/* Expertise Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3 mb-12">
            <TabsTrigger value="ai-platforms" className="flex items-center gap-2">
              <Brain className="h-4 w-4" />
              AI Platforms
            </TabsTrigger>
            <TabsTrigger value="intelligent-automation" className="flex items-center gap-2">
              <Workflow className="h-4 w-4" />
              Automation
            </TabsTrigger>
            <TabsTrigger value="custom-software" className="flex items-center gap-2">
              <Code className="h-4 w-4" />
              Custom Software
            </TabsTrigger>
          </TabsList>

          {Object.entries(expertiseAreas).map(([key, area]) => (
            <TabsContent key={key} value={key} className="space-y-8">
              <div className="text-center mb-12">
                <div className="w-20 h-20 bg-gradient-to-r from-primary-600 to-accent-500 rounded-full flex items-center justify-center mx-auto mb-6 text-white">
                  {area.icon}
                </div>
                <h2 className="text-3xl font-bold mb-4">{area.title}</h2>
                <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                  {area.description}
                </p>
              </div>

              <div className="grid md:grid-cols-2 gap-6">
                {area.services.map((service, index) => (
                  <Card key={index} className="group hover:shadow-lg transition-all duration-300">
                    <CardContent className="p-6">
                      <h3 className="text-xl font-semibold mb-3 group-hover:text-primary-600 transition-colors">
                        {service.title}
                      </h3>
                      <p className="text-muted-foreground mb-4">
                        {service.description}
                      </p>
                      <div className="flex flex-wrap gap-2">
                        {service.technologies.map((tech, techIndex) => (
                          <Badge key={techIndex} variant="secondary" className="text-xs">
                            {tech}
                          </Badge>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>
          ))}
        </Tabs>

        {/* CTA Section */}
        <section className="mt-20 text-center">
          <Card className="bg-gradient-to-r from-primary-50 to-accent-50 dark:from-primary-950 dark:to-accent-950 border-0">
            <CardContent className="p-12">
              <h2 className="text-3xl font-bold mb-6">Ready to Leverage Our Expertise?</h2>
              <p className="text-lg text-muted-foreground mb-8 max-w-2xl mx-auto">
                Let's discuss how our specialized knowledge can accelerate your AI transformation
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <a
                  href="/contact"
                  className="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background bg-primary-600 text-primary-foreground hover:bg-primary-700 h-10 py-2 px-4"
                >
                  Start Your Project
                </a>
                <a
                  href="/projects"
                  className="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 py-2 px-4"
                >
                  View Our Work
                </a>
              </div>
            </CardContent>
          </Card>
        </section>
      </div>
    </div>
  );
};

export default Expertise;
