import { renderHook, act } from '@testing-library/react';
import { useStore, useContactForm, useTheme } from '../useStore';

describe('useStore', () => {
  beforeEach(() => {
    // Clear localStorage before each test
    localStorage.clear();
  });

  it('initializes with default values', () => {
    const { result } = renderHook(() => useStore());
    
    expect(result.current.theme).toBe('dark');
    expect(result.current.isMobileMenuOpen).toBe(false);
    expect(result.current.isLoading).toBe(false);
    expect(result.current.prefersReducedMotion).toBe(false);
    expect(result.current.contactFormData).toEqual({
      name: '',
      email: '',
      message: '',
      interest: '',
    });
  });

  it('updates theme correctly', () => {
    const { result } = renderHook(() => useStore());
    
    act(() => {
      result.current.setTheme('light');
    });
    
    expect(result.current.theme).toBe('light');
  });

  it('toggles mobile menu correctly', () => {
    const { result } = renderHook(() => useStore());
    
    act(() => {
      result.current.setMobileMenuOpen(true);
    });
    
    expect(result.current.isMobileMenuOpen).toBe(true);
    
    act(() => {
      result.current.setMobileMenuOpen(false);
    });
    
    expect(result.current.isMobileMenuOpen).toBe(false);
  });

  it('updates contact form data correctly', () => {
    const { result } = renderHook(() => useStore());
    
    act(() => {
      result.current.setContactFormData({
        name: 'John Doe',
        email: '<EMAIL>',
      });
    });
    
    expect(result.current.contactFormData.name).toBe('John Doe');
    expect(result.current.contactFormData.email).toBe('<EMAIL>');
    expect(result.current.contactFormData.message).toBe(''); // Should preserve other fields
  });

  it('resets contact form correctly', () => {
    const { result } = renderHook(() => useStore());
    
    // First set some data
    act(() => {
      result.current.setContactFormData({
        name: 'John Doe',
        email: '<EMAIL>',
        message: 'Test message',
        interest: 'AI Platform',
      });
    });
    
    // Then reset
    act(() => {
      result.current.resetContactForm();
    });
    
    expect(result.current.contactFormData).toEqual({
      name: '',
      email: '',
      message: '',
      interest: '',
    });
  });
});

describe('useContactForm selector', () => {
  it('returns contact form data and functions', () => {
    const { result } = renderHook(() => useContactForm());
    
    expect(result.current.data).toEqual({
      name: '',
      email: '',
      message: '',
      interest: '',
    });
    expect(typeof result.current.setData).toBe('function');
    expect(typeof result.current.reset).toBe('function');
  });
});

describe('useTheme selector', () => {
  it('returns current theme', () => {
    const { result } = renderHook(() => useTheme());
    
    expect(result.current).toBe('dark');
  });
});
