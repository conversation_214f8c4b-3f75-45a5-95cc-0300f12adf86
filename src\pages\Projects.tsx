import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON> } from "react-router-dom";
import { ArrowRight, TrendingUp, Users, Zap, Brain, Server, Code } from "lucide-react";

const Projects = () => {
  const projects = [
    {
      slug: "ai-platform",
      title: "Metamorphic Labs – AI Platform",
      description: "Next-generation AI research and development platform with adaptive workflows, multi-model chaining, and automated optimization",
      image: "https://images.unsplash.com/photo-1677442136019-21780ecad995?w=600&h=400&fit=crop",
      category: "AI Platforms",
      status: "Conceptual/Design Phase",
      metrics: [
        { label: "AI Models", value: "Multi", icon: <Brain className="h-4 w-4" /> },
        { label: "Features", value: "50+", icon: <Zap className="h-4 w-4" /> },
        { label: "Integrations", value: "IDE+", icon: <Code className="h-4 w-4" /> }
      ],
      tags: ["AI Research", "Multi-Model", "Automation", "Custom Training"]
    },
    {
      slug: "ai-integration-broker",
      title: "AI Integration & Broker System",
      description: "Seamless integration of multiple AI models with intelligent query routing and multi-AI collaboration capabilities",
      image: "https://images.unsplash.com/photo-**********-ef010cbdcc31?w=600&h=400&fit=crop",
      category: "AI Infrastructure",
      status: "In Progress",
      metrics: [
        { label: "AI Services", value: "10+", icon: <Brain className="h-4 w-4" /> },
        { label: "Security", value: "AES-256", icon: <Zap className="h-4 w-4" /> },
        { label: "Integration", value: "OAuth", icon: <Users className="h-4 w-4" /> }
      ],
      tags: ["OAuth", "API Integration", "Multi-AI", "Broker System"]
    },
    {
      slug: "saas-suite",
      title: "Metamorphic SaaS Suite",
      description: "AI/ML-driven platform for generating and deploying custom SaaS applications on demand with blockchain revenue sharing",
      image: "https://images.unsplash.com/photo-*************-afdab827c52f?w=600&h=400&fit=crop",
      category: "SaaS Generation",
      status: "Conceptual Planning",
      metrics: [
        { label: "Architecture", value: "Multi-Tenant", icon: <Server className="h-4 w-4" /> },
        { label: "Revenue", value: "EFX Tokens", icon: <TrendingUp className="h-4 w-4" /> },
        { label: "Compliance", value: "Auto", icon: <Zap className="h-4 w-4" /> }
      ],
      tags: ["SaaS", "Blockchain", "EFX Tokens", "Marketplace"]
    },
    {
      slug: "living-pipeline",
      title: "Metamorphic Living Pipeline",
      description: "AI-optimized CI/CD pipeline system that self-optimizes and adapts continuously with ML-driven performance analysis",
      image: "https://images.unsplash.com/photo-1518432031352-d6fc5c10da5a?w=600&h=400&fit=crop",
      category: "DevOps Infrastructure",
      status: "Conceptual Design",
      metrics: [
        { label: "Architecture", value: "Event-Driven", icon: <Server className="h-4 w-4" /> },
        { label: "Optimization", value: "AI-Enhanced", icon: <Brain className="h-4 w-4" /> },
        { label: "Security", value: "OAuth2/RBAC", icon: <Zap className="h-4 w-4" /> }
      ],
      tags: ["CI/CD", "Kubernetes", "AI Optimization", "Go"]
    },
    {
      slug: "reactor-extension",
      title: "Metamorphic Reactor",
      description: "VS Code extension for AI-powered developer workflows with multi-agent coordination and multi-LLM logic",
      image: "https://images.unsplash.com/photo-1461749280684-dccba630e2f6?w=600&h=400&fit=crop",
      category: "Developer Tools",
      status: "Beta (v0.9.0)",
      metrics: [
        { label: "Version", value: "v0.9.0", icon: <Code className="h-4 w-4" /> },
        { label: "Agents", value: "Multi", icon: <Brain className="h-4 w-4" /> },
        { label: "Consensus", value: "100%", icon: <Zap className="h-4 w-4" /> }
      ],
      tags: ["VS Code", "Multi-Agent", "AutoGen", "Python"]
    },
    {
      slug: "deepseek-integration",
      title: "DeepSeek AI Integration",
      description: "Secure integration and forking strategy for DeepSeek AI with compliance safeguards and self-hosting capabilities",
      image: "https://images.unsplash.com/photo-1620712943543-bcc4688e7485?w=600&h=400&fit=crop",
      category: "AI Risk Management",
      status: "Strategic Planning",
      metrics: [
        { label: "Compliance", value: "GDPR/CCPA", icon: <Zap className="h-4 w-4" /> },
        { label: "Hosting", value: "Self-Hosted", icon: <Server className="h-4 w-4" /> },
        { label: "Independence", value: "Forked", icon: <Brain className="h-4 w-4" /> }
      ],
      tags: ["DeepSeek", "Compliance", "Self-Hosting", "AI Independence"]
    },
    {
      slug: "mcp-integration",
      title: "Model Context Protocol Integration",
      description: "MCP servers for persistent AI context across interactions with multi-model orchestration and blockchain integration",
      image: "https://images.unsplash.com/photo-1639322537228-f710d846310a?w=600&h=400&fit=crop",
      category: "AI Infrastructure",
      status: "Conceptual Design",
      metrics: [
        { label: "Context", value: "Persistent", icon: <Brain className="h-4 w-4" /> },
        { label: "Models", value: "Multi-AI", icon: <Zap className="h-4 w-4" /> },
        { label: "Network", value: "DeepMCP", icon: <Server className="h-4 w-4" /> }
      ],
      tags: ["MCP", "Context Management", "Blockchain", "EFX Tokens"]
    },
    {
      slug: "minecraft-deepmcp",
      title: "Minecraft Control Panel (DeepMCP)",
      description: "AI-enhanced Minecraft server management with blockchain economy and decentralized hosting on MetamorphicNet",
      image: "https://images.unsplash.com/photo-1542751371-adc38448a05e?w=600&h=400&fit=crop",
      category: "Gaming Infrastructure",
      status: "Concept Stage",
      metrics: [
        { label: "Economy", value: "AIQ-G Token", icon: <TrendingUp className="h-4 w-4" /> },
        { label: "Hosting", value: "Decentralized", icon: <Server className="h-4 w-4" /> },
        { label: "AI Features", value: "Auto-Scaling", icon: <Brain className="h-4 w-4" /> }
      ],
      tags: ["Minecraft", "Blockchain", "Gaming", "AI NPCs"]
    }
  ];

  const categoryColors = {
    "AI Platforms": "bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-200",
    "AI Infrastructure": "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",
    "SaaS Generation": "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
    "DevOps Infrastructure": "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200",
    "Developer Tools": "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200",
    "AI Risk Management": "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
    "Gaming Infrastructure": "bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200",
    "AI Model Suite": "bg-accent-100 text-accent-800 dark:bg-accent-900 dark:text-accent-200"
  };

  return (
    <div className="min-h-screen py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-5xl font-bold mb-6">
            Metamorphic Labs <span className="text-gradient">Projects</span>
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Discover our comprehensive ecosystem of AI platforms, infrastructure, and tools designed to revolutionize development and deployment
          </p>
        </div>

        {/* Projects Grid */}
        <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
          {projects.map((project, index) => (
            <Card key={project.slug} className="group hover:shadow-xl transition-all duration-300 overflow-hidden">
              <div className="relative overflow-hidden">
                <img
                  src={project.image}
                  alt={project.title}
                  className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                />
                <div className="absolute top-4 left-4">
                  <Badge className={categoryColors[project.category as keyof typeof categoryColors]}>
                    {project.category}
                  </Badge>
                </div>
                <div className="absolute top-4 right-4">
                  <Badge variant="outline" className="bg-white/90 dark:bg-black/90">
                    {project.status}
                  </Badge>
                </div>
              </div>
              
              <CardContent className="p-6">
                <h3 className="text-xl font-semibold mb-3 group-hover:text-primary-600 transition-colors">
                  {project.title}
                </h3>
                <p className="text-muted-foreground mb-4 line-clamp-2">
                  {project.description}
                </p>

                {/* Metrics */}
                <div className="grid grid-cols-3 gap-2 mb-4">
                  {project.metrics.map((metric, metricIndex) => (
                    <div key={metricIndex} className="text-center">
                      <div className="flex items-center justify-center mb-1 text-primary-600">
                        {metric.icon}
                      </div>
                      <div className="text-sm font-semibold">{metric.value}</div>
                      <div className="text-xs text-muted-foreground">{metric.label}</div>
                    </div>
                  ))}
                </div>

                {/* Tags */}
                <div className="flex flex-wrap gap-1 mb-4">
                  {project.tags.slice(0, 3).map((tag, tagIndex) => (
                    <Badge key={tagIndex} variant="outline" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                  {project.tags.length > 3 && (
                    <Badge variant="outline" className="text-xs">
                      +{project.tags.length - 3}
                    </Badge>
                  )}
                </div>

                <Link 
                  to={`/projects/${project.slug}`}
                  className="inline-flex items-center text-primary-600 hover:text-primary-700 font-medium text-sm group-hover:translate-x-1 transition-transform duration-200"
                >
                  View Details <ArrowRight className="ml-1 h-4 w-4" />
                </Link>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* CTA Section */}
        <section className="mt-20 text-center">
          <Card className="bg-gradient-to-r from-primary-50 to-accent-50 dark:from-primary-950 dark:to-accent-950 border-0">
            <CardContent className="p-12">
              <h2 className="text-3xl font-bold mb-6">Join the Metamorphic Ecosystem</h2>
              <p className="text-lg text-muted-foreground mb-8 max-w-2xl mx-auto">
                Be part of the next generation of AI-powered development tools and infrastructure
              </p>
              <Link
                to="/contact"
                className="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background bg-primary-600 text-primary-foreground hover:bg-primary-700 h-10 py-2 px-4"
              >
                Get Involved Today
              </Link>
            </CardContent>
          </Card>
        </section>
      </div>
    </div>
  );
};

export default Projects;
