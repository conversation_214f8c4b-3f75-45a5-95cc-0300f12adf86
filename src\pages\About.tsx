
import React from 'react';
import { motion } from 'framer-motion';
import { <PERSON>, <PERSON>Content, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button-gradient";
import { SEOHead } from "@/components/SEOHead";
import { ArrowR<PERSON>, Target, Eye, Heart, Users, Zap, Brain, Code } from "lucide-react";
import { Link } from "react-router-dom";

const About = () => {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6, ease: 'easeOut' }
    }
  };

  const coreValues = [
    {
      icon: <Brain className="h-8 w-8" />,
      title: "Innovation First",
      description: "We push the boundaries of what's possible with AI and quantum systems, constantly exploring new frontiers in technology."
    },
    {
      icon: <Users className="h-8 w-8" />,
      title: "Human-Centered Design",
      description: "Our solutions are built with people in mind, ensuring AI enhances human capabilities rather than replacing them."
    },
    {
      icon: <Zap className="h-8 w-8" />,
      title: "Rapid Iteration",
      description: "We believe in fast prototyping, continuous learning, and adapting quickly to emerging technologies and market needs."
    },
    {
      icon: <Code className="h-8 w-8" />,
      title: "Open Collaboration",
      description: "We foster transparency, knowledge sharing, and collaborative development across our ecosystem of platforms and tools."
    }
  ];

  const teamMembers = [
    {
      name: "Vernox",
      role: "Chief AI Architect",
      bio: "Visionary leader in AI systems architecture with expertise in multi-agent orchestration and quantum computing applications.",
      avatar: "V",
      gradient: "from-primary to-secondary"
    },
    {
      name: "0a",
      role: "Lead Systems Engineer",
      bio: "Expert in scalable infrastructure and distributed systems, specializing in high-performance AI platform development.",
      avatar: "0a",
      gradient: "from-secondary to-accent"
    },
    {
      name: "Nyra",
      role: "Creative Technology Director",
      bio: "Pioneer in generative art and NFT ecosystems, bridging the gap between AI creativity and blockchain technology.",
      avatar: "N",
      gradient: "from-gold to-yellow-400"
    }
  ];

  return (
    <>
      <SEOHead
        title="About Us - Our Mission, Vision & Team"
        description="Learn about Metamorphic Labs' mission to redefine reality through AI, quantum systems, and intelligent software. Meet our team of visionaries and architects building the future."
        keywords="About Metamorphic Labs, AI Team, Mission Vision, Vernox, 0a, Nyra, AI Architects, Quantum Systems Team"
        url="https://metamorphiclabs.ai/about"
      />
      <div className="min-h-screen bg-black py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h1 className="text-4xl md:text-5xl font-bold mb-6 text-white">
            About <span className="text-gradient">Metamorphic Labs</span>
          </h1>
          <p className="text-xl text-gray-300 max-w-4xl mx-auto leading-relaxed">
            We are pioneers in redefining reality through AI, quantum systems, and intelligent software.
            Our mission is to build the future of human-AI collaboration and push the boundaries of what's possible.
          </p>
        </motion.div>

        {/* Mission & Vision */}
        <section className="mb-20">
          <motion.div
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            className="grid md:grid-cols-2 gap-8"
          >
            <motion.div variants={itemVariants}>
              <Card className="h-full bg-gradient-primary border-0 hover:glow-primary transition-all duration-300">
                <CardContent className="p-8">
                  <div className="flex items-center mb-6">
                    <Target className="h-8 w-8 text-white mr-3" />
                    <h2 className="text-2xl font-bold text-white">Our Mission</h2>
                  </div>
                  <p className="text-white/90 leading-relaxed">
                    To pioneer next-generation AI systems, multi-agent orchestration, and generative creativity
                    that redefine reality. We build the future by creating intelligent software solutions that
                    push the boundaries of what's possible in human-AI collaboration.
                  </p>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div variants={itemVariants}>
              <Card className="h-full bg-gradient-to-br from-slate-900 to-slate-800 border-slate-700 hover:glow-secondary transition-all duration-300">
                <CardContent className="p-8">
                  <div className="flex items-center mb-6">
                    <Eye className="h-8 w-8 text-white mr-3" />
                    <h2 className="text-2xl font-bold text-white">Our Vision</h2>
                  </div>
                  <p className="text-gray-300 leading-relaxed">
                    A world where AI, quantum systems, and intelligent software seamlessly integrate with human
                    creativity to solve complex challenges. From deep prompt ecosystems to decentralized art
                    galleries—we envision a future built on intelligent collaboration.
                  </p>
                </CardContent>
              </Card>
            </motion.div>
          </motion.div>
        </section>

        {/* Core Values */}
        <section className="mb-20">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl font-bold text-white mb-4">
              Our <span className="text-gradient">Core Values</span>
            </h2>
            <p className="text-gray-300 max-w-2xl mx-auto">
              The principles that guide our work and shape our approach to building the future
            </p>
          </motion.div>

          <motion.div
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            className="grid md:grid-cols-2 lg:grid-cols-4 gap-6"
          >
            {coreValues.map((value, index) => (
              <motion.div key={index} variants={itemVariants}>
                <Card className="h-full bg-gradient-to-br from-slate-900 to-slate-800 border-slate-700 hover:border-primary/50 hover:glow-primary transition-all duration-300 group">
                  <CardContent className="p-6 text-center">
                    <motion.div
                      className="w-16 h-16 bg-gradient-primary rounded-xl flex items-center justify-center mb-4 mx-auto"
                      whileHover={{ scale: 1.1, rotate: 5 }}
                    >
                      <div className="text-white">
                        {value.icon}
                      </div>
                    </motion.div>
                    <h3 className="text-lg font-semibold mb-3 text-white">{value.title}</h3>
                    <p className="text-gray-300 text-sm leading-relaxed">{value.description}</p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </motion.div>
        </section>

        {/* Team Section */}
        <section className="mb-20">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl font-bold text-white mb-4">
              Meet Our <span className="text-gradient">Team</span>
            </h2>
            <p className="text-gray-300 max-w-2xl mx-auto">
              The visionaries and architects behind Metamorphic Labs' revolutionary AI systems
            </p>
          </motion.div>

          <motion.div
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto"
          >
            {teamMembers.map((member, index) => (
              <motion.div key={index} variants={itemVariants}>
                <Card className={`h-full group hover:shadow-2xl transition-all duration-300 ${
                  member.name === 'Nyra'
                    ? 'vault-024-card hover:glow-gold'
                    : 'bg-gradient-to-br from-slate-900 to-slate-800 border-slate-700 hover:border-primary/50 hover:glow-primary'
                }`}>
                  <CardContent className="p-8 text-center">
                    <motion.div
                      className={`w-24 h-24 bg-gradient-to-r ${member.gradient} rounded-full mx-auto mb-6 flex items-center justify-center`}
                      whileHover={{ scale: 1.1, rotate: 5 }}
                    >
                      <span className={`font-bold text-xl ${
                        member.name === 'Nyra' ? 'text-black' : 'text-white'
                      }`}>
                        {member.avatar}
                      </span>
                    </motion.div>
                    <h3 className={`text-xl font-semibold mb-2 ${
                      member.name === 'Nyra' ? 'vault-024-text' : 'text-white'
                    }`}>
                      {member.name}
                    </h3>
                    <p className={`font-medium mb-4 ${
                      member.name === 'Nyra' ? 'text-gold/80' : 'text-gradient'
                    }`}>
                      {member.role}
                    </p>
                    <p className={`leading-relaxed ${
                      member.name === 'Nyra' ? 'text-gold/70' : 'text-gray-300'
                    }`}>
                      {member.bio}
                    </p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </motion.div>
        </section>

        {/* CTA Section */}
        <motion.section
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center"
        >
          <Card className="bg-gradient-primary border-0">
            <CardContent className="p-12">
              <h2 className="text-3xl font-bold text-white mb-4">
                Ready to Build the Future Together?
              </h2>
              <p className="text-xl text-white/90 mb-8 max-w-3xl mx-auto">
                Join us in redefining reality through AI, quantum systems, and intelligent software.
                Let's create something extraordinary.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button variant="vault" size="lg" asChild>
                  <Link to="/contact">Get in Touch</Link>
                </Button>
                <Button variant="gradient-outline" size="lg" asChild>
                  <Link to="/systems">Explore Our Systems</Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </motion.section>
        </div>
      </div>
    </>
  );
};

export default About;
