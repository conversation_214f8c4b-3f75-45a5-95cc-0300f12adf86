import React from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button-gradient';
import { Badge } from '@/components/ui/badge';
import { ArrowRight, ExternalLink } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ProjectCardProps {
  title: string;
  description: string;
  url?: string;
  status: 'active' | 'development' | 'concept';
  type: 'catalyst' | 'reactor' | 'vault024';
  features?: string[];
  className?: string;
}

const projectTypeStyles = {
  catalyst: {
    card: 'bg-gradient-primary border-primary/20 hover:border-primary/40',
    title: 'text-white',
    description: 'text-white/80',
    button: 'gradient' as const,
  },
  reactor: {
    card: 'bg-gradient-primary border-secondary/20 hover:border-secondary/40',
    title: 'text-white',
    description: 'text-white/80',
    button: 'gradient' as const,
  },
  vault024: {
    card: 'vault-024-card border-2',
    title: 'vault-024-text',
    description: 'text-gold/80',
    button: 'vault' as const,
  },
};

const statusColors = {
  active: 'bg-green-500 text-white',
  development: 'bg-yellow-500 text-black',
  concept: 'bg-blue-500 text-white',
};

export const ProjectCard: React.FC<ProjectCardProps> = ({
  title,
  description,
  url,
  status,
  type,
  features = [],
  className,
}) => {
  const styles = projectTypeStyles[type];
  
  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { duration: 0.6, ease: 'easeOut' }
    },
    hover: { 
      y: -8,
      transition: { duration: 0.3, ease: 'easeOut' }
    }
  };

  return (
    <motion.div
      variants={cardVariants}
      initial="hidden"
      animate="visible"
      whileHover="hover"
      className={cn('group', className)}
    >
      <Card className={cn(
        'relative overflow-hidden transition-all duration-300 hover:shadow-2xl',
        styles.card,
        type === 'vault024' && 'hover:glow-gold',
        (type === 'catalyst' || type === 'reactor') && 'hover:glow-primary'
      )}>
        <CardContent className="p-6">
          {/* Status Badge */}
          <div className="flex justify-between items-start mb-4">
            <Badge className={statusColors[status]} variant="secondary">
              {status.charAt(0).toUpperCase() + status.slice(1)}
            </Badge>
            {type === 'vault024' && (
              <div className="text-gold text-sm font-mono">024</div>
            )}
          </div>

          {/* Title */}
          <h3 className={cn(
            'text-xl font-bold mb-3 group-hover:text-gradient transition-all duration-300',
            styles.title
          )}>
            {title}
          </h3>

          {/* Description */}
          <p className={cn('text-sm mb-4 leading-relaxed', styles.description)}>
            {description}
          </p>

          {/* Features */}
          {features.length > 0 && (
            <div className="mb-4">
              <div className="flex flex-wrap gap-1">
                {features.slice(0, 3).map((feature, index) => (
                  <Badge
                    key={index}
                    variant="outline"
                    className={cn(
                      'text-xs',
                      type === 'vault024' 
                        ? 'border-gold/30 text-gold/70' 
                        : 'border-white/30 text-white/70'
                    )}
                  >
                    {feature}
                  </Badge>
                ))}
                {features.length > 3 && (
                  <Badge
                    variant="outline"
                    className={cn(
                      'text-xs',
                      type === 'vault024' 
                        ? 'border-gold/30 text-gold/70' 
                        : 'border-white/30 text-white/70'
                    )}
                  >
                    +{features.length - 3} more
                  </Badge>
                )}
              </div>
            </div>
          )}

          {/* Action Button */}
          <div className="flex gap-2">
            {url ? (
              <Button
                variant={styles.button}
                size="sm"
                className="flex-1"
                asChild
              >
                <a href={url} target="_blank" rel="noopener noreferrer">
                  <span>Explore</span>
                  <ExternalLink className="ml-2 h-4 w-4" />
                </a>
              </Button>
            ) : (
              <Button
                variant={type === 'vault024' ? 'vault-outline' : 'gradient-outline'}
                size="sm"
                className="flex-1"
                disabled
              >
                <span>Coming Soon</span>
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            )}
          </div>

          {/* Holographic Effect */}
          <div className="absolute inset-0 holographic pointer-events-none" />
        </CardContent>
      </Card>
    </motion.div>
  );
};
