import { test, expect } from '@playwright/test';

test.describe('Metamorphic Labs Website', () => {
  test('should load the homepage', async ({ page }) => {
    await page.goto('/');
    
    // Check that the page loads
    await expect(page).toHaveTitle(/Metamorphic Labs/);
    
    // Check for main navigation
    await expect(page.locator('nav')).toBeVisible();
    
    // Check for hero section
    await expect(page.locator('h1')).toBeVisible();
  });

  test('should navigate to about page', async ({ page }) => {
    await page.goto('/');

    // Check if we're on mobile (viewport width < 768px)
    const viewport = page.viewportSize();
    const isMobile = viewport && viewport.width < 768;

    if (isMobile) {
      // Open mobile menu first
      await page.click('[aria-label="Toggle menu"]');
      await page.waitForTimeout(500); // Wait for animation
    }

    // Click on About link
    await page.click('text=About');

    // Check URL
    await expect(page).toHaveURL('/about');

    // Check page content
    await expect(page.locator('h1')).toContainText('About');
  });

  test('should navigate to contact page', async ({ page }) => {
    await page.goto('/');

    // Check if we're on mobile (viewport width < 768px)
    const viewport = page.viewportSize();
    const isMobile = viewport && viewport.width < 768;

    if (isMobile) {
      // Open mobile menu first
      await page.click('[aria-label="Toggle menu"]');
      await page.waitForTimeout(500); // Wait for animation
    }

    // Click on Contact link
    await page.click('text=Contact');

    // Check URL
    await expect(page).toHaveURL('/contact');

    // Check for contact form - be more specific
    await expect(page.locator('form').first()).toBeVisible();
    await expect(page.locator('input[name="name"]')).toBeVisible();
    await expect(page.locator('input[name="email"]')).toBeVisible();
  });

  test('should have responsive navigation', async ({ page }) => {
    // Test mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    await page.goto('/');
    
    // Mobile menu button should be visible
    await expect(page.locator('[aria-label="Toggle menu"]')).toBeVisible();
  });
});
