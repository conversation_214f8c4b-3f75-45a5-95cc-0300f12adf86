
import { <PERSON> } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { CheckCircle, ArrowRight, Calendar, Mail } from "lucide-react";

const Thanks = () => {
  return (
    <div className="min-h-screen flex items-center justify-center py-12">
      <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <div className="animate-fade-in">
          <div className="w-20 h-20 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto mb-8">
            <CheckCircle className="h-10 w-10 text-green-600" />
          </div>
          
          <h1 className="text-4xl md:text-5xl font-bold mb-6">
            Thank You!
          </h1>
          
          <p className="text-xl text-muted-foreground mb-8">
            Your message has been received successfully. We'll get back to you within 24 hours.
          </p>

          <Card className="mb-8">
            <CardContent className="p-8">
              <h2 className="text-2xl font-semibold mb-4">What happens next?</h2>
              <div className="space-y-4 text-left">
                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span className="text-xs font-semibold text-primary-600">1</span>
                  </div>
                  <div>
                    <h3 className="font-semibold">Review & Analysis</h3>
                    <p className="text-muted-foreground">We'll review your project details and requirements</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span className="text-xs font-semibold text-primary-600">2</span>
                  </div>
                  <div>
                    <h3 className="font-semibold">Initial Response</h3>
                    <p className="text-muted-foreground">You'll receive our initial response within 24 hours</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span className="text-xs font-semibold text-primary-600">3</span>
                  </div>
                  <div>
                    <h3 className="font-semibold">Discovery Call</h3>
                    <p className="text-muted-foreground">We'll schedule a call to discuss your project in detail</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button asChild>
              <Link to="/projects">
                View Our Work <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
            <Button variant="outline" asChild>
              <Link to="/">
                Back to Home
              </Link>
            </Button>
          </div>

          <div className="mt-12 pt-8 border-t border-border">
            <p className="text-muted-foreground mb-4">
              Have an urgent question? Reach out directly:
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="mailto:<EMAIL>"
                className="inline-flex items-center text-primary-600 hover:text-primary-700"
              >
                <Mail className="h-4 w-4 mr-2" />
                <EMAIL>
              </a>
              <a
                href="tel:+15551234567"
                className="inline-flex items-center text-primary-600 hover:text-primary-700"
              >
                <Calendar className="h-4 w-4 mr-2" />
                +****************
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Thanks;
